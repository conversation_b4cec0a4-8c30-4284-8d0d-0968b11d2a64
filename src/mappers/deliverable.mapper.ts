import { DeliverableViewDto } from 'src/dtos/responses/deliverable-view.dto';
import { DeliverableEntity } from '../entities/deliverable.entity';
import { ProjectEntity } from '../entities/project.entity';

export function mapToView(entity: DeliverableEntity | ProjectEntity): DeliverableViewDto {
  const isProject = 'deliverables' in entity;

  const base: DeliverableViewDto = {
    uid: entity.uid,
    name: entity.name,
    businessFunction: entity.function,
    frequency: entity.frequency,
    isActive: entity.is_active,
    usage: entity.usage ?? Math.ceil(Math.random() * 100),
    source: entity.source,
    owners: (entity as any).owners ?? [],
    calculationMethod: (entity as any).calculation_method ?? '',
    definition: (entity as any).definition ?? '',
    paValue: (entity as any).pa_value ?? '',
    buLevelAggregation: (entity as any).bu_level_aggregation ?? '',
    type: (entity as any).deliverable_type?.code,
    projectType: (entity as any).project_type,
  };

  if (isProject) {
    return {
      ...base,
      objective: (entity as any).objective ?? '',
      dateStart: (entity as any).date_start ?? null,
      dateEnd: (entity as any).date_end ?? null,
      deliverables: ((entity as any).deliverables ?? []).map((d: any) => mapToView(d)),
    };
  }

  return base;
}