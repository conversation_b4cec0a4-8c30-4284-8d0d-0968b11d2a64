import { GetProjectResponse } from 'src/dtos/responses/get-project.dto';
import { ProjectType } from 'src/enums';
import { ProjectEntity } from '../entities/project.entity';
import { DeliverableEntity } from '../entities/deliverable.entity';

export function mapProjectToView(entity: ProjectEntity): GetProjectResponse {
  return {
    uid: entity.uid,
    name: entity.name,
    businessFunction: entity.function,
    objective: entity.objective ?? '',
    projectType: entity.project_type as ProjectType,
    dateStart: entity.date_start ?? null,
    dateEnd: entity.date_end ?? null,
    deliverables: entity.deliverables?.map((d: DeliverableEntity) => d.uid) || [],
  };
}
