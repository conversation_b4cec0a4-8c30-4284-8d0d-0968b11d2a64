import { FindOptionsWhere, Repository, UpdateResult } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { BaseEntity } from '../entities/base.entity';

export class BaseRepository<T extends BaseEntity> {
  constructor(protected readonly repository: Repository<T>) { }

  /**
   * Creates an entity
   * @param entity T
   * @returns T
   */
  create(entity: T): Promise<T> {
    return this.repository.save(entity);
  }

  /**
   * Updates an entity by uid
   * @param uid string|number - The unique identifier of the entity
   * @param entity Partial<T> - The partial entity data to update
   * @returns Promise<T> - The updated entity
   */
  update = async (
    uid: string | number,
    entity: Partial<T>,
  ): Promise<T> => {
    const existingEntity = await this.repository.findOneBy({
      uid,
    } as unknown as FindOptionsWhere<T>);

    if (!existingEntity) {
      throw new Error(`Entity with uid ${uid} not found`);
    }

    const updatedEntity = { ...existingEntity, ...entity };
    return await this.repository.save(updatedEntity);
  };

  /**
   * Updates entities by filter criteria
   * @param options FindOptionsWhere<T> - The filter criteria
   * @param entity QueryDeepPartialEntity<T> - The partial entity data to update
   * @returns Promise<UpdateResult> - The update result
   */
  updateByFilter = (
    options: FindOptionsWhere<T>,
    entity: QueryDeepPartialEntity<T>,
  ): Promise<UpdateResult> => this.repository.update(options, entity);

  /**
   * Soft deletes an entity by uid
   * @param uid string|number - The unique identifier of the entity to delete
   * @param deleted_by string - UUID of the user performing the deletion
   */
  async delete(
    uid: string | number,
    deleted_by: string,
  ): Promise<void> {
    await this.repository.update(
      { uid } as unknown as FindOptionsWhere<T>,
      {
        deleted_by,
        deleted_at: new Date(),
      } as unknown as QueryDeepPartialEntity<T>,
    );
  }

  /**
   * Finds all entities from the repository
   * @returns Promise<T[]> A list of all entities
   */
  async find(): Promise<T[]> {
    return this.repository.find();
  }

  /**
   * Finds all active entities (not soft deleted)
   * @returns Promise<T[]> A list of all active entities
   */
  async findActive(): Promise<T[]> {
    return this.repository.find({
      where: { deleted_at: null } as unknown as FindOptionsWhere<T>,
    });
  }
}
