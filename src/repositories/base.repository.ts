import { FindOptionsWhere, Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { BaseEntity } from '../entities/base.entity';

export class BaseRepository<T extends BaseEntity> {
  constructor(protected readonly repository: Repository<T>) { }

  /**
   * Creates an entity
   * @param entity T
   * @returns T
   */
  create(entity: T): Promise<T> {
    return this.repository.save(entity);
  }

  /**
   * Updates an entity
   * @param primaryKey string|number
   * @param entity T
   * @returns T
   */
  update = async (
    primaryKeyColumn: string,
    primaryKeyValue: string | number,
    entity: any,
  ) => {
    const existingEntity = await this.repository.findOneBy({
      [primaryKeyColumn]: primaryKeyValue,
    } as unknown as FindOptionsWhere<T>);
    const updatedEntity = { ...existingEntity, ...entity };
    return await this.repository.save(updatedEntity);
  };

  /**
   * Updates an entity
   * @param primaryKey string|number
   * @param entity T
   * @returns T
   */
  updateByFilter = (
    options: FindOptionsWhere<T>,
    entity: QueryDeepPartialEntity<T>,
  ) => this.repository.update(options, entity);

  /**
   * Soft deletes an entity by uid
   * @param uid string|number
   * @param deleted_by string - UUID of the user performing the deletion
   */
  async delete(
    uid: string | number,
    deleted_by: string,
  ): Promise<void> {
    await this.repository.update(
      { uid } as any,
      {
        deleted_by,
        deleted_at: new Date(),
      } as any,
    );
  }

  /**
     * Finds all entities from the repository
     * 
     * @returns Promise<T[]> A list of all entities
     */
  async find(): Promise<T[]> {
    return this.repository.find();
  }
}
