import { Injectable, NotFoundException } from '@nestjs/common';
import { GetDeliverablesRequest } from '../../dtos/requests/get-deliverables.dto';
import { GetDeliverablesCompactResponse } from '../../dtos/responses/get-deliverables-compact.dto';
import { ProjectRepository } from '../../repositories/project.repository';
import { mapToView } from '../../mappers/deliverable.mapper';
import { DeliverableRepository } from '../../repositories/deliverable.repository';
import { GetDeliverableResponse } from '../../dtos/responses/get-deliverable.dto';

@Injectable()
export class CatalogService {
  constructor(
    private readonly deliverableRepository: DeliverableRepository,
    private readonly projectRepository: ProjectRepository,
  ) { }

  async getDeliverableOrProject(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponse> {
    console.log(sessionUserUuid);

    const project = await this.projectRepository.findByUid(uid);
    if (project) return mapToView(project);

    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (deliverable) return mapToView(deliverable);

    throw new NotFoundException(`KPI or Project with uid ${uid} not found.`);
  }

  async getDeliverablesAndProjects(
    filters: GetDeliverablesRequest,
    sessionUserUuid: string,
  ): Promise<GetDeliverablesCompactResponse> {
    console.log(sessionUserUuid);

    const normalizedFilters = this.normalizeFilters(filters);
    const { search, businessFunctions, isActive, type } = normalizedFilters;

    const fetchOptions = this.determineFetchOptions(type);
    const results = await this.fetchFilteredData(
      fetchOptions,
      search,
      businessFunctions,
      isActive,
    );

    return this.combineResults(results);
  }

  private normalizeFilters(
    filters: GetDeliverablesRequest,
  ): GetDeliverablesRequest {
    if (typeof filters.businessFunctions === 'string') {
      try {
        const parsed = JSON.parse(filters.businessFunctions);
        if (Array.isArray(parsed)) {
          filters.businessFunctions = parsed.map(String);
        } else {
          filters.businessFunctions = [String(filters.businessFunctions)];
        }
      } catch {
        filters.businessFunctions = [String(filters.businessFunctions)];
      }
    } else if (filters.businessFunctions != null && !Array.isArray(filters.businessFunctions)) {
      filters.businessFunctions = [String(filters.businessFunctions)];
    }
    return filters;
  }

  private determineFetchOptions(type?: string[]): {
    fetchProjects: boolean;
    fetchDeliverables: boolean;
  } {
    const fetchProjects = type ? type.includes('Project') : true;
    const fetchDeliverables = type ? type.includes('Deliverable') : true;
    return { fetchProjects, fetchDeliverables };
  }

  private async fetchFilteredData(
    {
      fetchProjects,
      fetchDeliverables,
    }: { fetchProjects: boolean; fetchDeliverables: boolean },
    search?: string,
    businessFunctions?: string[],
    isActive?: boolean,
  ): Promise<[GetDeliverablesCompactResponse, GetDeliverablesCompactResponse]> {
    const emptyResult = {
      data: [],
      pageNumber: 1,
      pageSize: 0,
      totalRecords: 0,
    };

    if (!fetchProjects && !fetchDeliverables) {
      return [emptyResult, emptyResult];
    }

    if (fetchProjects && fetchDeliverables) {
      return Promise.all([
        this.projectRepository.findCompactWithFilters(
          search,
          businessFunctions,
          isActive,
        ),
        this.deliverableRepository.findCompactStandaloneWithFilters(
          search,
          businessFunctions,
          isActive,
        ),
      ]);
    }

    if (fetchProjects) {
      const projects = await this.projectRepository.findCompactWithFilters(
        search,
        businessFunctions,
        isActive,
      );
      return [projects, emptyResult];
    }

    const deliverables =
      await this.deliverableRepository.findCompactStandaloneWithFilters(
        search,
        businessFunctions,
        isActive,
      );
    return [emptyResult, deliverables];
  }

  private combineResults([projects, deliverables]: [
    GetDeliverablesCompactResponse,
    GetDeliverablesCompactResponse,
  ]): GetDeliverablesCompactResponse {
    return {
      data: [...projects.data, ...deliverables.data],
      pageNumber: 1,
      pageSize: projects.data.length + deliverables.data.length,
      totalRecords: projects.totalRecords + deliverables.totalRecords,
    };
  }
}
