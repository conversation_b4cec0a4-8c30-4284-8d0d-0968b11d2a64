import {
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateDeliverableRequest } from '../../dtos/requests/create-deliverable.dto';
import { UpdateDeliverableRequest } from '../../dtos/requests/update-deliverable.dto';
import { GetDeliverableResponse } from '../../dtos/responses/get-deliverable.dto';
import { GetDeliverablesResponse } from '../../dtos/responses/get-deliverables.dto';
import { DeliverableRepository } from '../../repositories/deliverable.repository';
import { mapToView } from '../../mappers/deliverable.mapper';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class DeliverablesService {
  constructor(
    private readonly deliverableRepository: DeliverableRepository,
  ) { }

  async createDeliverable(
    deliverableDto: CreateDeliverableRequest,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponse> {
    const entity = this.deliverableRepository.repository.create({
      ...deliverableDto,
      function: deliverableDto.businessFunction,
      is_active: deliverableDto.isActive,
      calculation_method: deliverableDto.calculationMethod,
      pa_value: deliverableDto.paValue,
      bu_level_aggregation: deliverableDto.buLevelAggregation,
      created_at: new Date(),
      created_by: sessionUserUuid || uuidv4(),
      origin: 'CATALOG',
    });

    const saved = await this.deliverableRepository.repository.save(entity);

    return mapToView(saved);
  }

  async updateDeliverable(
    uid: string,
    deliverableDto: UpdateDeliverableRequest,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponse> {
    const deliverable = await this.deliverableRepository.findByUid(uid);
    if (!deliverable) {
      throw new NotFoundException(`KPI with uid ${uid} not found.`);
    }

    Object.assign(deliverable, {
      ...deliverableDto,
      function: deliverableDto.businessFunction,
      is_active: deliverableDto.isActive,
      calculation_method: deliverableDto.calculationMethod,
      pa_value: deliverableDto.paValue,
      bu_level_aggregation: deliverableDto.buLevelAggregation,
      updated_at: new Date(),
      updated_by: sessionUserUuid || uuidv4(),
    });

    const saved = await this.deliverableRepository.repository.save(deliverable);
    return mapToView(saved);
  }

  async softDeleteDeliverable(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetDeliverableResponse> {
    const deliverable = await this.deliverableRepository.findByUid(uid);

    if (!deliverable) {
      throw new NotFoundException(`Deliverable with uid ${uid} not found.`);
    }

    await this.deliverableRepository.delete(uid, sessionUserUuid);

    return mapToView(deliverable);
  }

  async getDeliverablesFunctions(): Promise<string[]> {
    const response = await this.deliverableRepository.getFunctions();

    if (!response || response.length === 0) {
      return [];
    }

    return response.map((item) => item.function);
  }

  async getAll(sessionUserUuid: string): Promise<GetDeliverablesResponse> {
    console.log(sessionUserUuid);

    const entities = await this.deliverableRepository.repository.find({
      relations: ['deliverable_type', 'origin_project', 'usages'],
      order: { name: 'ASC' },
    });

    const mappedEntities = entities.map((entity) => mapToView(entity));

    return {
      data: mappedEntities,
      pageNumber: 1,
      pageSize: mappedEntities.length,
      totalRecords: mappedEntities.length,
    };
  }
}
