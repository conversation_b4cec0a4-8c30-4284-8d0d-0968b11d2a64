import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { User, UserSession } from '../../decorators/user.decorator';
import { CreateProjectRequest } from '../../dtos/requests/create-project.dto';
import { UpdateProjectRequest } from '../../dtos/requests/update-project.dto';
import { GetProjectResponse } from '../../dtos/responses/get-project.dto';
import { GetProjectsResponse } from '../../dtos/responses/get-projects.dto';
import { ProjectsService } from './projects.service';

@ApiTags('Projects')
@Controller('projects')
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) { }

  @Get()
  @ApiOperation({
    summary: 'Get All Projects',
    description: 'Retrieves a complete list of all projects in the catalog with full details including associated deliverables'
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved all projects',
    type: 'GetProjectsResponse'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async getAll(@User() user: UserSession): Promise<GetProjectsResponse> {
    return this.projectsService.getAll(user?.uuid);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create Project',
    description: 'Creates a new project in the catalog with the provided details and associated deliverables'
  })
  @ApiBody({
    type: CreateProjectRequest,
    description: 'Project creation data'
  })
  @ApiResponse({
    status: 201,
    description: 'Project successfully created',
    type: 'GetProjectResponse'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data'
  })
  @ApiResponse({
    status: 404,
    description: 'One or more referenced deliverables not found'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async createProject(
    @Body() projectDto: CreateProjectRequest,
    @User() user: UserSession,
  ): Promise<GetProjectResponse> {
    return this.projectsService.createProject(projectDto, user?.uuid);
  }

  @Put(':uid')
  @ApiOperation({
    summary: 'Update Project',
    description: 'Updates an existing project with the provided data and deliverable associations'
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the project to update',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: UpdateProjectRequest,
    description: 'Project update data'
  })
  @ApiResponse({
    status: 200,
    description: 'Project successfully updated',
    type: 'GetProjectResponse'
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data'
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found or one or more referenced deliverables not found'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  async updateProject(
    @Param('uid') uid: string,
    @Body() projectDto: UpdateProjectRequest,
    @User() user: UserSession,
  ): Promise<GetProjectResponse> {
    return this.projectsService.updateProject(uid, projectDto, user?.uuid);
  }

  @Delete(':uid')
  @ApiOperation({
    summary: 'Soft Delete Project',
    description: 'Performs a soft delete on a project (marks as deleted without removing from database)'
  })
  @ApiParam({
    name: 'uid',
    description: 'Unique identifier of the project to delete',
    type: 'string',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: 200,
    description: 'Project successfully soft deleted',
    type: 'GetProjectResponse'
  })
  @ApiResponse({
    status: 404,
    description: 'Project not found'
  })
  @ApiResponse({
    status: 501,
    description: 'Not implemented yet'
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error'
  })
  softDeleteDeliverable(
    @Param('uid') uid: string,
    @User() user: UserSession,
  ): Promise<GetProjectResponse> {
    return this.projectsService.softDeleteDeliverable(uid, user?.uuid);
  }
}
