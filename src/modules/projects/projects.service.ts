import {
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateProjectRequest } from '../../dtos/requests/create-project.dto';
import { UpdateProjectRequest } from '../../dtos/requests/update-project.dto';
import { GetProjectResponse } from '../../dtos/responses/get-project.dto';
import { GetProjectsResponse } from '../../dtos/responses/get-projects.dto';
import { ProjectRepository } from '../../repositories/project.repository';
import { DeliverableRepository } from '../../repositories/deliverable.repository';
import { mapProjectToView } from '../../mappers/project.mapper';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class ProjectsService {
  constructor(
    private readonly projectRepository: ProjectRepository,
    private readonly deliverableRepository: DeliverableRepository,
  ) { }

  async createProject(
    projectDto: CreateProjectRequest,
    sessionUserUuid: string,
  ): Promise<GetProjectResponse> {
    let deliverablesEntities = [];

    if (projectDto.deliverables && projectDto.deliverables.length > 0) {
      deliverablesEntities = await this.deliverableRepository.findByUids(
        projectDto.deliverables,
      );

      const foundUids = deliverablesEntities.map((d) => d.uid);
      const missingUids = projectDto.deliverables.filter(
        (uid) => !foundUids.includes(uid),
      );

      if (missingUids.length > 0) {
        throw new NotFoundException(
          `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
        );
      }
    }

    const entity = this.projectRepository.repository.create({
      ...projectDto,
      function: projectDto.businessFunction,
      project_type: projectDto.projectType,
      date_start: projectDto.dateStart,
      date_end: projectDto.dateEnd,
      deliverables: deliverablesEntities,
      created_at: new Date(),
      created_by: sessionUserUuid || uuidv4(),
      origin: 'CATALOG',
    });

    const saved = await this.projectRepository.repository.save(entity);

    return mapProjectToView(saved);
  }

  async updateProject(
    uid: string,
    projectDto: UpdateProjectRequest,
    sessionUserUuid: string,
  ): Promise<GetProjectResponse> {
    const project = await this.projectRepository.findByUid(uid);
    if (!project) {
      throw new NotFoundException(`Project with uid ${uid} not found.`);
    }

    let deliverablesEntities = project.deliverables;

    if (projectDto.deliverables !== undefined) {
      if (projectDto.deliverables.length > 0) {
        deliverablesEntities = await this.deliverableRepository.findByUids(
          projectDto.deliverables,
        );

        const foundUids = deliverablesEntities.map((d) => d.uid);
        const missingUids = projectDto.deliverables.filter(
          (uid) => !foundUids.includes(uid),
        );

        if (missingUids.length > 0) {
          throw new NotFoundException(
            `Deliverable(s) with uid(s) ${missingUids.join(', ')} not found.`,
          );
        }
      } else {
        deliverablesEntities = [];
      }
    }

    Object.assign(project, {
      ...projectDto,
      function: projectDto.businessFunction,
      project_type: projectDto.projectType,
      date_start: projectDto.dateStart,
      date_end: projectDto.dateEnd,
      deliverables: deliverablesEntities,
      updated_at: new Date(),
      updated_by: sessionUserUuid || uuidv4(),
    });

    const saved = await this.projectRepository.repository.save(project);
    return mapProjectToView(saved);
  }

  async softDeleteDeliverable(
    uid: string,
    sessionUserUuid: string,
  ): Promise<GetProjectResponse> {
    const project = await this.projectRepository.findByUid(uid);

    if (!project) {
      throw new NotFoundException(`Project with uid ${uid} not found.`);
    }
    await this.projectRepository.delete(uid, sessionUserUuid);

    return mapProjectToView(project);
  }

  async getAll(sessionUserUuid: string): Promise<GetProjectsResponse> {
    console.log(sessionUserUuid);

    const entities = await this.projectRepository.repository.find({
      relations: ['deliverables', 'deliverables.deliverable_type'],
      order: { name: 'ASC' },
    });

    const mappedEntities = entities.map((entity) => mapProjectToView(entity));

    return {
      data: mappedEntities,
      pageNumber: 1,
      pageSize: mappedEntities.length,
      totalRecords: mappedEntities.length,
    };
  }
}
