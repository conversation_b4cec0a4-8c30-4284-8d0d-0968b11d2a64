import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

export class CompactDeliverableViewDto {
  @ApiProperty({
    description: 'Unique identifier for the deliverable or project',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  uid: string;

  @ApiProperty({
    description: 'Name of the deliverable or project',
    example: 'Monthly Revenue Report',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Business function or department',
    example: 'Finance',
  })
  @IsString()
  businessFunction: string;

  @ApiProperty({
    description: 'Type classification (Deliverable or Project)',
    example: 'Deliverable',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Usage score or priority level',
    example: 85,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  usage: number;

  @ApiProperty({
    description: 'Whether the item is currently active',
    example: true,
  })
  @IsString()
  isActive: boolean;

  @ApiProperty({
    description: 'Calculation method used for the deliverable or project',
    nullable: true,
  })
  @IsString()
  calculationMethod?: string;

  @ApiProperty({
    description: 'Value of the performance indicator',
    nullable: true,
  })
  @IsString()
  paValue?: string;

  @ApiProperty({
    description: 'Definition of the deliverable or project',
    nullable: true,
  })
  @IsString()
  definition?: string;

  @ApiProperty({
    description: 'Indicates whether the item is a Deliverable or a Project',
    example: 'Deliverable',
  })
  @IsString()
  item_type: 'Deliverable' | 'Project';
}
