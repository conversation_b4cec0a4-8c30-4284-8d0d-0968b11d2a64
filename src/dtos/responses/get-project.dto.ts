import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsDate, IsEnum, IsArray } from 'class-validator';
import { Type } from 'class-transformer';
import { ProjectType } from '../../enums';

export class GetProjectResponse {
  @ApiProperty({
    description: 'Unique identifier for the project',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsString()
  uid: string;

  @ApiProperty({
    description: 'Name of the project',
    example: 'Q4 Revenue Optimization Initiative'
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Business function or department this project belongs to',
    example: 'Finance'
  })
  @IsOptional()
  @IsString()
  businessFunction?: string;

  @ApiPropertyOptional({
    description: 'Business objective or goal this project aims to achieve',
    example: 'Increase quarterly revenue by 15% through process optimization'
  })
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiProperty({
    description: 'Classification type of this project',
    enum: ProjectType,
    example: ProjectType.SIMPLE
  })
  @IsEnum(ProjectType)
  projectType: ProjectType;

  @ApiPropertyOptional({
    description: 'Project start date',
    type: Date,
    example: '2024-01-01T00:00:00.000Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateStart?: Date;

  @ApiPropertyOptional({
    description: 'Project end date',
    type: Date,
    example: '2024-12-31T23:59:59.999Z'
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  dateEnd?: Date;

  @ApiPropertyOptional({
    description: 'Array of deliverable UIDs that are part of this project',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************', '987fcdeb-51a2-43d1-9f4e-123456789abc']
  })
  @IsOptional()
  @IsArray()
  deliverables?: string[];
}
