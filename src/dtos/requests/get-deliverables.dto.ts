import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsIn, IsOptional, IsString } from 'class-validator';
import { PaginatedRequest } from './paginated.dto';
import { Transform } from 'class-transformer';
import { TransformBoolean } from 'src/helpers/tranformer.helper';

export class GetDeliverablesRequest extends PaginatedRequest {
  @ApiPropertyOptional({
    description: 'Text search term to filter deliverables and projects by name',
    example: 'revenue',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Array of business function names to filter by',
    type: [String],
    isArray: true,
    example: ['Finance', 'Marketing', 'Operations'],
  })
  @IsOptional()
  businessFunctions?: string[];

  @ApiPropertyOptional({
    description:
      'Filter by active status. True for active items, false for inactive, omit for all',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(TransformBoolean)
  isActive?: boolean;

  @ApiPropertyOptional({
    description:
      'Filter by entity type. Include "Deliverable" for deliverables, "Project" for projects, or both',
    type: [String],
    enum: ['Deliverable', 'Project'],
    isArray: true,
    example: ['Deliverable', 'Project'],
  })
  @IsOptional()
  @IsIn(['Deliverable', 'Project'], { each: true })
  type?: ('Deliverable' | 'Project')[];
}
