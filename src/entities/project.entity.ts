import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { DeliverableEntity } from './deliverable.entity';
import { BaseEntity } from './base.entity';

@Entity({ name: 'kpi_project' })
export class ProjectEntity extends BaseEntity {
  @Column()
  name: string;

  @Column()
  function: string;

  @Column()
  frequency: string;

  @Column()
  is_active: boolean;

  @Column()
  source: string;

  @Column()
  usage: number;

  @Column({ nullable: true })
  objective: string;

  @Column({ type: 'datetime2' })
  date_start: Date;

  @Column({ type: 'datetime2' })
  date_end: Date;

  @Column()
  project_type: string;

  @OneToMany(
    () => DeliverableEntity,
    (deliverable) => deliverable.origin_project,
  )
  deliverables: DeliverableEntity[];

  @ManyToMany(() => DeliverableEntity)
  @JoinTable({
    name: 'kpi_project_deliverables',
    joinColumn: {
      name: 'code_project',
      referencedColumnName: 'uid',
    },
    inverseJoinColumn: {
      name: 'code_deliverable',
      referencedColumnName: 'uid',
    },
  })
  deliverable_links: DeliverableEntity[];

  item_type: 'Project';
}
